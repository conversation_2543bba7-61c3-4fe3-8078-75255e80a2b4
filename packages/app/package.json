{"name": "app", "version": "0.0.0", "private": true, "bundled": true, "backstage": {"role": "frontend"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "clean": "backstage-cli package clean", "test": "backstage-cli package test", "lint": "backstage-cli package lint"}, "dependencies": {"@backstage-community/plugin-entity-feedback": "^0.8.0", "@backstage-community/plugin-tech-radar": "^1.8.0", "@backstage/app-defaults": "^1.6.3", "@backstage/canon": "^0.5.0", "@backstage/catalog-model": "^1.7.4", "@backstage/cli": "^0.33.0", "@backstage/core-app-api": "^1.17.1", "@backstage/core-components": "^0.17.3", "@backstage/core-plugin-api": "^1.10.8", "@backstage/integration-react": "^1.2.8", "@backstage/plugin-api-docs": "^0.12.8", "@backstage/plugin-catalog": "^1.31.0", "@backstage/plugin-catalog-common": "^1.1.4", "@backstage/plugin-catalog-graph": "^0.4.20", "@backstage/plugin-catalog-import": "^0.13.1", "@backstage/plugin-catalog-react": "^1.19.0", "@backstage/plugin-kubernetes": "^0.12.8", "@backstage/plugin-org": "^0.6.40", "@backstage/plugin-permission-react": "^0.4.35", "@backstage/plugin-scaffolder": "^1.32.0", "@backstage/plugin-search": "^1.4.27", "@backstage/plugin-search-react": "^1.9.1", "@backstage/plugin-techdocs": "^1.13.1", "@backstage/plugin-techdocs-module-addons-contrib": "^1.1.25", "@backstage/plugin-techdocs-react": "^1.3.0", "@backstage/plugin-user-settings": "^0.8.23", "@backstage/theme": "^0.6.6", "@material-ui/core": "^4.12.2", "@material-ui/icons": "^4.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "react": "^18.0.2", "react-dom": "^18.0.2", "react-router": "^6.3.0", "react-router-dom": "^6.3.0"}, "devDependencies": {"@backstage/test-utils": "^1.7.9", "@playwright/test": "^1.32.3", "@testing-library/dom": "^9.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/react-dom": "^19.1.6", "cross-env": "^7.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "files": ["dist"]}