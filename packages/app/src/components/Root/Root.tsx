import { PropsWithChildren } from 'react';
import { SidebarProvider, SidebarTrigger, SidebarInset } from '../ui/sidebar';
import { AppSidebar } from '../AppSidebar';



export const Root = ({ children }: PropsWithChildren<{}>) => (
  <SidebarProvider>
    <AppSidebar />
    <SidebarInset>
      <header style={{
        display: 'flex',
        height: '3rem',
        alignItems: 'center',
        gap: '1rem',
        padding: '0 1rem',
        borderBottom: '1px solid var(--sidebar-border)',
        backgroundColor: 'var(--sidebar-background)'
      }}>
        <SidebarTrigger />
        <div style={{ fontSize: '1.125rem', fontWeight: '600' }}>
          Backstage
        </div>
      </header>
      <main style={{ flex: 1, padding: '1rem' }}>
        {children}
      </main>
    </SidebarInset>
  </SidebarProvider>
);