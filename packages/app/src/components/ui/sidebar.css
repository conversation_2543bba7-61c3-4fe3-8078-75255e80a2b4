/* Sidebar CSS Variables */
:root {
  --sidebar-background: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

.dark {
  --sidebar-background: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(0 0% 98%);
  --sidebar-primary-foreground: hsl(240 5.9% 10%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

/* Basic utility classes */
.flex { display: flex; }
.hidden { display: none; }
.min-h-svh { min-height: 100svh; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.flex-1 { flex: 1 1 0%; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.p-2 { padding: 0.5rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.font-medium { font-weight: 500; }
.rounded-md { border-radius: 0.375rem; }
.border { border-width: 1px; }
.border-r { border-right-width: 1px; }
.bg-background { background-color: hsl(var(--background)); }
.bg-sidebar { background-color: var(--sidebar-background); }
.text-sidebar-foreground { color: var(--sidebar-foreground); }
.border-sidebar-border { border-color: var(--sidebar-border); }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.hover\:bg-sidebar-accent:hover { background-color: var(--sidebar-accent); }
.hover\:text-sidebar-accent-foreground:hover { color: var(--sidebar-accent-foreground); }
.focus-visible\:outline-none:focus-visible { outline: 2px solid transparent; outline-offset: 2px; }
.focus-visible\:ring-1:focus-visible { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.disabled\:pointer-events-none:disabled { pointer-events: none; }
.disabled\:opacity-50:disabled { opacity: 0.5; }

/* Sidebar specific styles */
.group\/sidebar-wrapper {
  display: flex;
  min-height: 100svh;
  width: 100%;
}

.group\/sidebar-wrapper.has-inset {
  background-color: var(--sidebar-background);
}

/* Hide on mobile by default */
@media (max-width: 767px) {
  .sidebar-variant-sidebar,
  .sidebar-variant-floating,
  .sidebar-variant-inset {
    display: none;
  }
}

/* Sidebar variants */
.sidebar-variant-sidebar {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 10;
  display: none;
  width: var(--sidebar-width, 16rem);
  flex-shrink: 0;
  background-color: var(--sidebar-background);
  color: var(--sidebar-foreground);
  border-right: 1px solid var(--sidebar-border);
}

.sidebar-variant-floating {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 10;
  display: none;
  width: var(--sidebar-width, 16rem);
  flex-shrink: 0;
  background-color: var(--sidebar-background);
  color: var(--sidebar-foreground);
  border: 1px solid var(--sidebar-border);
  border-radius: 0 0.5rem 0.5rem 0;
  padding: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.sidebar-variant-inset {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 10;
  display: none;
  width: var(--sidebar-width, 16rem);
  flex-shrink: 0;
  background-color: var(--sidebar-background);
  color: var(--sidebar-foreground);
  border-right: 1px solid var(--sidebar-border);
}

/* Show sidebar on medium screens and up */
@media (min-width: 768px) {
  .sidebar-variant-sidebar,
  .sidebar-variant-floating,
  .sidebar-variant-inset {
    display: block;
  }
}

/* Collapsed state */
.sidebar-collapsed.sidebar-collapsible-icon {
  width: var(--sidebar-width-icon, 3rem);
}

/* Mobile sidebar */
.sidebar-mobile {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 50;
  height: auto;
  width: var(--sidebar-width-mobile, 18rem);
  background-color: var(--sidebar-background);
  padding: 0;
  color: var(--sidebar-foreground);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: transform 200ms ease-in-out;
}

.sidebar-mobile.sidebar-side-left {
  left: 0;
}

.sidebar-mobile.sidebar-side-right {
  right: 0;
}

.sidebar-mobile.sidebar-closed.sidebar-side-left {
  transform: translateX(-100%);
}

.sidebar-mobile.sidebar-closed.sidebar-side-right {
  transform: translateX(100%);
}

.sidebar-mobile.sidebar-open {
  transform: translateX(0);
}

/* Mobile overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
}

/* Sidebar content */
.sidebar-content {
  display: flex;
  min-height: 0;
  flex: 1 1 0%;
  flex-direction: column;
  gap: 0.5rem;
  overflow: auto;
}

.sidebar-content.sidebar-collapsible-icon {
  overflow: hidden;
}

/* Sidebar group */
.sidebar-group {
  position: relative;
  display: flex;
  width: 100%;
  min-width: 0;
  flex-direction: column;
  padding: 0.5rem;
}

/* Sidebar group label */
.sidebar-group-label {
  display: flex;
  height: 2rem;
  flex-shrink: 0;
  align-items: center;
  border-radius: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--sidebar-foreground);
  opacity: 0.7;
  outline: 2px solid transparent;
  outline-offset: 2px;
  transition-property: margin, opacity;
  transition-duration: 200ms;
  transition-timing-function: linear;
}

.sidebar-group-label:focus-visible {
  box-shadow: 0 0 0 2px var(--sidebar-ring);
}

.sidebar-group-label.sidebar-collapsible-icon {
  margin-top: -2rem;
  opacity: 0;
}

/* Sidebar menu */
.sidebar-menu {
  display: flex;
  width: 100%;
  min-width: 0;
  flex-direction: column;
  gap: 0.25rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar-menu-item {
  position: relative;
}

/* Sidebar menu button */
.sidebar-menu-button {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 0.5rem;
  overflow: hidden;
  border-radius: 0.375rem;
  padding: 0.5rem;
  text-align: left;
  font-size: 0.875rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
  transition-property: width, height, padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  background: transparent;
  border: none;
  color: var(--sidebar-foreground);
  cursor: pointer;
  text-decoration: none;
}

.sidebar-menu-button:hover {
  background-color: var(--sidebar-accent);
  color: var(--sidebar-accent-foreground);
}

.sidebar-menu-button:focus-visible {
  box-shadow: 0 0 0 2px var(--sidebar-ring);
}

.sidebar-menu-button:active {
  background-color: var(--sidebar-accent);
  color: var(--sidebar-accent-foreground);
}

.sidebar-menu-button:disabled {
  pointer-events: none;
  opacity: 0.5;
}

.sidebar-menu-button[data-active="true"] {
  background-color: var(--sidebar-accent);
  font-weight: 500;
  color: var(--sidebar-accent-foreground);
}

.sidebar-menu-button.sidebar-collapsible-icon {
  width: 2rem !important;
  height: 2rem !important;
  padding: 0.5rem !important;
}

/* Sidebar trigger */
.sidebar-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  height: 1.75rem;
  width: 1.75rem;
  background: transparent;
  border: none;
  color: var(--sidebar-foreground);
  cursor: pointer;
}

.sidebar-trigger:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 1px var(--sidebar-ring);
}

.sidebar-trigger:disabled {
  pointer-events: none;
  opacity: 0.5;
}

.sidebar-trigger:hover {
  background-color: var(--sidebar-accent);
  color: var(--sidebar-accent-foreground);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Additional utility classes for better layout */
.sidebar-header,
.sidebar-footer {
  padding: 0.5rem;
}

.sidebar-content {
  padding: 0.5rem;
}

.sidebar-group-content {
  width: 100%;
}

.sidebar-separator {
  margin: 0.5rem;
  height: 1px;
  background-color: var(--sidebar-border);
}

/* Icon sizing */
.sidebar-menu-button svg {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* Text truncation */
.sidebar-menu-button span:last-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
