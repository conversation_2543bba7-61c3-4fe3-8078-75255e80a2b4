# Shadcn-UI Sidebar Implementation for Backstage

This directory contains a custom implementation of the shadcn-ui sidebar components, adapted to work with Backstage's existing Material-UI setup.

## What was replaced

The original Backstage sidebar in `packages/app/src/components/Root/Root.tsx` used Backstage's built-in sidebar components:
- `Sidebar`
- `SidebarPage`
- `SidebarGroup`
- `SidebarItem`
- etc.

## What was implemented

### New Components

1. **`sidebar.tsx`** - Main sidebar component library with:
   - `SidebarProvider` - Context provider for sidebar state
   - `Sidebar` - Main sidebar container
   - `SidebarTrigger` - Button to toggle sidebar
   - `SidebarInset` - Main content area wrapper
   - `SidebarHeader`, `SidebarFooter`, `SidebarContent` - Layout components
   - `SidebarGroup`, `SidebarGroupLabel`, `SidebarGroupContent` - Grouping components
   - `SidebarMenu`, `SidebarMenuItem`, `SidebarMenuButton` - Menu components
   - `SidebarSeparator` - Visual separator
   - `useSidebar` - Hook for sidebar state management

2. **`sidebar.css`** - Custom CSS styles that replicate shadcn-ui styling without requiring Tailwind CSS

3. **`AppSidebar.tsx`** - New sidebar implementation using the shadcn-ui components

4. **`utils.ts`** - Utility functions for className merging

### Features

- **Responsive Design**: Automatically adapts to mobile/desktop
- **Collapsible**: Can be toggled open/closed
- **Keyboard Shortcut**: Ctrl/Cmd + B to toggle
- **State Persistence**: Remembers open/closed state via cookies
- **Accessible**: Proper ARIA labels and keyboard navigation
- **Customizable**: Easy to modify styling and behavior

### Usage

The sidebar is now integrated into the Root component:

```tsx
export const Root = ({ children }: PropsWithChildren<{}>) => (
  <SidebarProvider>
    <AppSidebar />
    <SidebarInset>
      <header>
        <SidebarTrigger />
        <div>Backstage</div>
      </header>
      <main>
        {children}
      </main>
    </SidebarInset>
  </SidebarProvider>
);
```

### Customization

You can customize the sidebar by:
1. Modifying the CSS variables in `sidebar.css`
2. Adding new menu items in `AppSidebar.tsx`
3. Changing the layout structure
4. Adding new sidebar components

### Dependencies Added

- `clsx` - For conditional className merging
- `class-variance-authority` - For component variants

The implementation maintains compatibility with existing Backstage components while providing the modern, flexible sidebar structure of shadcn-ui.
