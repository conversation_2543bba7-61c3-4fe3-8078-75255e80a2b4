import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from './ui/sidebar';
import './ui/sidebar.css';

// Import Material-UI icons (keeping existing ones)
import HomeIcon from '@material-ui/icons/Home';
import ExtensionIcon from '@material-ui/icons/Extension';
import RadarIcon from '@mui/icons-material/Radar';
import LibraryBooks from '@material-ui/icons/LibraryBooks';
import CreateComponentIcon from '@material-ui/icons/AddCircleOutline';
import SearchIcon from '@material-ui/icons/Search';
import GroupIcon from '@material-ui/icons/People';
import SettingsIcon from '@material-ui/icons/Settings';

// Import Backstage components we still need
import { Link } from '@backstage/core-components';
import { UserSettingsSignInAvatar } from '@backstage/plugin-user-settings';

// Navigation items data
const navigationItems = [
  {
    title: "Home",
    url: "/catalog",
    icon: HomeIcon,
  },
  {
    title: "APIs",
    url: "/api-docs",
    icon: ExtensionIcon,
  },
  {
    title: "Docs",
    url: "/docs",
    icon: LibraryBooks,
  },
  {
    title: "Create...",
    url: "/create",
    icon: CreateComponentIcon,
  },
];

const additionalItems = [
  {
    title: "Tech Radar",
    url: "/tech-radar",
    icon: RadarIcon,
  },
];

export function AppSidebar() {
  return (
    <Sidebar className="sidebar-variant-sidebar">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '0.5rem',
                  fontSize: '1.25rem',
                  fontWeight: 'bold'
                }}>
                  🎭 Backstage
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        {/* Search Group */}
        <SidebarGroup>
          <SidebarGroupLabel>Search</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/search" style={{ textDecoration: 'none', color: 'inherit' }}>
                    <SearchIcon />
                    <span>Search</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel>Menu</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link to={item.url} style={{ textDecoration: 'none', color: 'inherit' }}>
                      <item.icon />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
              
              {/* My Groups - keeping the original component */}
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/catalog?filters[kind]=group" style={{ textDecoration: 'none', color: 'inherit' }}>
                    <GroupIcon />
                    <span>My Groups</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Additional Plugins */}
        <SidebarGroup>
          <SidebarGroupLabel>Added Plugins</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {additionalItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link to={item.url} style={{ textDecoration: 'none', color: 'inherit' }}>
                      <item.icon />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Link to="/settings" style={{ textDecoration: 'none', color: 'inherit' }}>
                <UserSettingsSignInAvatar />
                <span>Settings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
