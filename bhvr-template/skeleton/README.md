# ${{ values.name }}

${{ values.description }}

## 🚀 BHVR Stack

This project uses the **BHVR** stack:
- **B**un - Fast JavaScript runtime and package manager
- **H**ono - Lightweight web framework for the backend
- **V**ite - Fast build tool for the frontend  
- **R**eact - Modern UI library

## 📁 Project Structure

```
${{ values.name }}/
├── client/          # React frontend (Vite)
├── server/          # Hono backend (Bun)
├── shared/          # Shared TypeScript definitions
│   └── src/types/   # Type definitions used by both client and server
└── package.json     # Root package.json with workspaces
```

## 🛠️ Getting Started

### Prerequisites

- [Bun](https://bun.sh/) >= 1.0.0

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ${{ values.name }}
```

2. Install dependencies:
```bash
bun install
```

### Development

Start all services in development mode:
```bash
bun run dev
```

This will start:
- Shared types in watch mode
- Server at http://localhost:${{ values.serverPort }}
- Client at http://localhost:${{ values.clientPort }}

### Individual Commands

Run specific parts of the project:
```bash
bun run dev:shared    # Watch shared types
bun run dev:server    # Start server only
bun run dev:client    # Start client only
```

### Building

Build the project for production:
```bash
bun run build
```

### Type Checking

Check TypeScript types across all packages:
```bash
bun run type-check
```

### Linting

Lint the client code:
```bash
bun run lint
```

## 🏗️ Architecture

### Shared Types

The `shared` package contains TypeScript definitions that are used by both the client and server, ensuring type safety across the full stack.

```typescript
import { ApiResponse } from 'shared/types';
```

### Backend (Hono)

The server uses Hono, a lightweight web framework that's perfect for edge computing and provides excellent TypeScript support.

### Frontend (React + Vite)

The client is built with React and Vite for fast development and optimized production builds.

{% if values.enableDatabase %}
## 🗄️ Database

This project is configured to use **${{ values.databaseType }}** as the database.

### Database Setup

1. Install database dependencies (if not already installed):
```bash
cd server && bun add @databases/${{ values.databaseType }}
```

2. Configure your database connection in `server/src/config/database.ts`

3. Run migrations (if applicable):
```bash
cd server && bun run migrate
```
{% endif %}

## 📝 Scripts

| Command | Description |
|---------|-------------|
| `bun run dev` | Start all services in development mode |
| `bun run build` | Build for production |
| `bun run type-check` | Check TypeScript types |
| `bun run lint` | Lint the code |
| `bun run clean` | Clean all node_modules |

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [BHVR Stack](https://github.com/stevedylandev/bhvr) - The original BHVR template
- [Backstage](https://backstage.io/) - Developer portal platform
