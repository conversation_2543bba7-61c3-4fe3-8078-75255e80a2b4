.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    '<PERSON>bunt<PERSON>', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

.app-header {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo h1 {
  font-size: 3rem;
  margin: 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tagline {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0.5rem 0 2rem 0;
}

.stack-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  backdrop-filter: blur(10px);
}

.stack-info h2 {
  margin-top: 0;
  font-size: 2rem;
}

.stack-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.stack-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stack-item strong {
  color: #4ecdc4;
  font-size: 1.2rem;
}

.api-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  backdrop-filter: blur(10px);
}

.api-button {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border: none;
  color: white;
  padding: 12px 24px;
  font-size: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  margin: 1rem 0;
}

.api-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.api-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.response, .error {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  text-align: left;
}

.error {
  border-left: 4px solid #ff6b6b;
}

.response {
  border-left: 4px solid #4ecdc4;
}

.response-content p {
  margin: 0.5rem 0;
}

.response-content details {
  margin-top: 1rem;
}

.response-content pre {
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.9rem;
}

.links {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  backdrop-filter: blur(10px);
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.link-grid a {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s;
}

.link-grid a:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.footer {
  margin-top: 3rem;
  opacity: 0.7;
  font-size: 0.9rem;
}

.footer p {
  margin: 0.25rem 0;
}

@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }
  
  .logo h1 {
    font-size: 2rem;
  }
  
  .stack-grid,
  .link-grid {
    grid-template-columns: 1fr;
  }
}
