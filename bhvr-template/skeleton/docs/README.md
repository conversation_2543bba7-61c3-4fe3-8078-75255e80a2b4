# ${{ values.name }} Documentation

Welcome to the documentation for your BHVR application!

## 📚 Table of Contents

- [Getting Started](#getting-started)
- [Architecture](#architecture)
- [Development Workflow](#development-workflow)
- [Deployment](#deployment)
- [API Documentation](#api-documentation)
- [Troubleshooting](#troubleshooting)

## 🚀 Getting Started

### Prerequisites

- [Bun](https://bun.sh/) >= 1.0.0
- Node.js >= 18.0.0 (for compatibility)
- Git

### Quick Start

1. **Clone and Setup**

   ```bash
   git clone <repository-url>
   cd ${{ values.name }}
   bun install
   ```

2. **Environment Configuration**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Development**
   ```bash
   bun run dev
   ```

## 🏗️ Architecture

### BHVR Stack Overview

- **Bun**: JavaScript runtime and package manager
- **Hono**: Web framework for the backend API
- **Vite**: Build tool and development server
- **React**: Frontend UI library

### Project Structure

```
${{ values.name }}/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── utils/          # Utility functions
│   │   └── types/          # Client-specific types
│   ├── public/             # Static assets
│   └── package.json
├── server/                 # Hono backend
│   ├── src/
│   │   ├── routes/         # API route handlers
│   │   ├── middleware/     # Custom middleware
│   │   ├── services/       # Business logic
│   │   ├── models/         # Data models
│   │   └── utils/          # Server utilities
│   └── package.json
├── shared/                 # Shared TypeScript types
│   ├── src/
│   │   └── types/          # Type definitions
│   └── package.json
└── docs/                   # Documentation
```

### Data Flow

1. **Client** makes HTTP requests to the server
2. **Server** processes requests using Hono routes
3. **Shared types** ensure type safety across the stack
4. **Database** (optional) stores persistent data

## 🔄 Development Workflow

### Running Services

```bash
# Start all services
bun run dev

# Start individual services
bun run dev:client    # Frontend only
bun run dev:server    # Backend only
bun run dev:shared    # Shared types watch mode
```

### Code Quality

```bash
# Type checking
bun run type-check

# Linting
bun run lint

# Testing
bun run test
```

### Adding New Features

1. **Define Types** in `shared/src/types/`
2. **Create API Routes** in `server/src/routes/`
3. **Build UI Components** in `client/src/components/`
4. **Add Tests** for new functionality

## 🚀 Deployment

### Build for Production

```bash
bun run build
```

### Environment Variables

Production environment variables:

```env
NODE_ENV=production
PORT=${{ values.serverPort }}
VITE_SERVER_URL=https://your-domain.com
{% if values.enableDatabase %}
DB_HOST=your-db-host
DB_PORT={% if values.databaseType == "postgresql" %}5432{% elif values.databaseType == "mysql" %}3306{% else %}N/A{% endif %}
DB_NAME=${{ values.name }}
DB_USER=your-db-user
DB_PASSWORD=your-db-password
{% endif %}
```

### Deployment Options

- **Vercel**: Deploy frontend and serverless functions
- **Railway**: Full-stack deployment with database
- **Docker**: Containerized deployment
- **VPS**: Traditional server deployment

## 📖 API Documentation

### Base URL

- Development: `http://localhost:${{ values.serverPort }}`
- Production: `https://your-domain.com`

### Endpoints

#### Health Check

```
GET /
GET /api/health
```

#### Hello World

```
GET /hello
```

Response:

```json
{
  "message": "Hello BHVR!",
  "success": true,
  "data": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "version": "1.0.0",
    "environment": "development"
  }
}
```

## 🔧 Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Kill process using the port
lsof -ti:${{ values.serverPort }} | xargs kill -9
lsof -ti:${{ values.clientPort }} | xargs kill -9
```

#### Type Errors

```bash
# Rebuild shared types
cd shared && bun run build
```

#### Module Resolution Issues

```bash
# Clear node_modules and reinstall
bun run clean
bun install
```

### Debug Mode

Enable debug logging:

```bash
DEBUG=* bun run dev
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.
