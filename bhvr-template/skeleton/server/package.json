{
  "name": "server",
  "version": "1.0.0",
  "description": "Hono backend server for ${{ values.name }}",
  "main": "src/index.ts",
  "scripts": {
    "dev": "bun run --hot src/index.ts",
    "start": "bun run src/index.ts",
    "build": "bun build src/index.ts --outdir dist --target bun",
    "type-check": "tsc --noEmit",
    "test": "bun test"
  },
  "dependencies": {
    "hono": "^3.12.0",
    "shared": "workspace:*"{% if values.enableDatabase %},
    {% if values.databaseType == "postgresql" %}"pg": "^8.11.0",
    "@types/pg": "^8.10.0"{% elif values.databaseType == "mysql" %}"mysql2": "^3.6.0"{% elif values.databaseType == "sqlite" %}"better-sqlite3": "^9.0.0",
    "@types/better-sqlite3": "^7.6.0"{% endif %}{% endif %}
  },
  "devDependencies": {
    "@types/bun": "latest",
    "typescript": "^5.0.0"
  }
}
